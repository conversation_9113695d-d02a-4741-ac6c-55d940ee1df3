#ifndef DEBUG_ASSIST_H
#define DEBUG_ASSIST_H

#ifdef __cplusplus
extern "C" {
#endif

/******************************************************************************
  
                                Includes     
  
 ******************************************************************************/

//============================> System Headers <============================
#include <stdint.h>
#include <string.h>
#include <stdbool.h>

//============================> Libraries Headers <============================
#include "dx.h"
#include "proto.h"

//============================> Project Headers <============================

/******************************************************************************
  
                                Defines     
  
 ******************************************************************************/

/******************************************************************************
  
                                Enums     
  
 ******************************************************************************/

typedef enum
{
    DA_AUTO_CALL_STATE_IDLE,
    DA_AUTO_CALL_STATE_PTT_PRESSED,
    DA_AUTO_CALL_STATE_PTT_RELEASED,
    DA_AUTO_CALL_STATE_CANCELED,
} da_auto_call_state_t;

/******************************************************************************
  
                                Struct     
  
 ******************************************************************************/

typedef struct s_da_call_statistic
{
    uint16_t call_attempt_count; // 呼叫尝试次数
    uint16_t call_success_count; // 呼叫成功次数
    uint16_t ptt_granted_count;  // PTT授权成功次数
    uint16_t call_fail_count;    // 呼叫失败次数
} da_call_statistic_t;

typedef struct s_debug_assist
{
    proto_timer_t *auto_call_timer; // 自动呼叫定时器
    da_auto_call_state_t auto_call_state;
    bool auto_call_enabled; // 是否启用自动呼叫

    // 统计信息
    da_call_statistic_t call_statistic;

    bool pending_call; // 是否有呼叫请求
    bool call_success; // 是否呼叫成功
    bool ptt_granted;  // 是否PTT授权成功
} debug_assist_t;

/******************************************************************************

                                Variables

 ******************************************************************************/

/******************************************************************************

                                Public Functions

 ******************************************************************************/

/**
 * @brief 启用/禁用自动呼叫
 */
void debug_assist_toggle_auto_call(bool enable);

/**
 * @brief 是否启用自动呼叫
 */
bool debug_assist_auto_call_enabled(void);

/**
 * @brief 更新呼叫成功状态
 */
void debug_assist_update_status_call_success(bool call_success);

/**
 * @brief 更新PTT授权状态
 */
void debug_assist_update_status_ptt_granted(bool ptt_granted);

/**
 * @brief 更新呼叫取消状态
 */
void debug_assist_update_status_call_canceled(void);

/**
 * @brief 重置呼叫统计信息
 */
void debug_assist_call_statistic_reset(void);

/**
 * @brief 读取呼叫统计信息
 */
uint16_t debug_assist_get_call_attempt_count(void);

/**
 * @brief 读取呼叫成功次数
 */
uint16_t debug_assist_get_call_success_count(void);

/**
 * @brief 读取PTT授权成功次数
 */
uint16_t debug_assist_get_ptt_granted_count(void);

/**
 * @brief 读取呼叫失败次数
 */
uint16_t debug_assist_get_call_fail_count(void);

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif
