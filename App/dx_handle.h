#ifndef DX_HANDLE_H
#define DX_HANDLE_H

#ifdef __cplusplus
extern "C" {
#endif

/******************************************************************************
  
                                Includes     
  
 ******************************************************************************/

//============================> System Headers <============================
#include <stdint.h>
#include <string.h>
#include <stdbool.h>

//============================> Libraries Headers <============================
#include "dx.h"
#include "proto.h"

//============================> Project Headers <============================


/******************************************************************************
  
                                Defines     
  
 ******************************************************************************/

/******************************************************************************
  
                                Enums     
  
 ******************************************************************************/

/******************************************************************************
  
                                Struct     
  
 ******************************************************************************/

typedef struct s_dx_handle
{
    proto_handle_t proto_handle; // 协议解析器句柄
    uint16_t frame_num;

    proto_timer_t *status_update_timer;            // 定时更新设备状态信息的定时器
    proto_timer_t *status_report_timer;            // 定期向POC模块(通过数传端口)上报设备状态信息的定时器
    proto_timer_t *voice_call_status_report_timer; // 当进入语音通话状态时, 定期向数传端口发送语音通话状态信息的定时器

    dx_ptt_state_t ptt_state; // PTT状态

    proto_rtx_monitor_t rtx_monitor;
} dx_handle_t;

/******************************************************************************

                                Variables

 ******************************************************************************/

extern const proto_handle_class_t dx_handle_class;

/******************************************************************************

                                Public Functions

 ******************************************************************************/

/**
 * @brief 创建数据交换层协议解析器句柄
 * 
 * @return proto_handle_t*          返回协议解析器句柄
 */
proto_handle_t *dx_handle_create(void);

/**
 * @brief 获取数据交换层协议解析器句柄实例
 * 
 * @return dx_handle_t *          返回协议解析器句柄
 */
dx_handle_t *dx_handle_get_instance(void);

/**
 * @brief 启动/停止设备状态上报定时器
 * 
 * @param handle                    数据交换层解析器句柄
 */
void dx_handle_toggle_status_report_timer(struct s_dx_handle *dx_handle, bool enable);

/**
 * @brief 启动/停止语音通话状态上报定时器
 * 
 * @param handle                    数据交换层解析器句柄
 */
void dx_handle_toggle_voice_call_status_report_timer(struct s_dx_handle *dx_handle, bool enable);

/**
 * @brief 发送呼叫请求帧
 * 
 * @param handle                    数据交换层解析器句柄
 * @param ig                        true: 组呼; false: 个呼
 * @param emg                       是否紧急呼叫: 当按下PTT键时, emg = true; 当按下PTT2键时, emg = false
 * @param callee_id                 被叫方ID
 */
void dx_handle_send_call_req(struct s_dx_handle *dx_handle, bool ig, bool emg, uint32_t callee_id);

/**
 * @brief 发送PTT话权请求帧
 * 
 * @param handle                    数据交换层解析器句柄
 * @param status                    DX_PTT_PRESS: 按下PTT键; DX_PTT_RELEASE: 松开PTT键
 */
void dx_handle_send_ptt_auth_req(struct s_dx_handle *dx_handle, dx_ptt_state_t ptt_state);

/**
 * @brief 发送结束呼叫请求帧
 * 
 * @param handle                    数据交换层解析器句柄
 */
void dx_handle_send_call_exit_req(struct s_dx_handle *dx_handle);

/**
 * @brief 发送POC用户信息
 */
int dx_handle_send_poc_user_info(dx_handle_t *handle);

/**
 * @brief 发送POC服务器信息
 */
int dx_handle_send_poc_server_info(dx_handle_t *handle);

/**
 * @brief 发送POC APN信息
 */
int dx_handle_send_poc_apn_info(dx_handle_t *handle);

/**
 * @brief 更新开机/关机状态
 * 
 * @note 此函数会通过数据传输端口发送设备的当前状态信息，并附带开机或关机的标志。
 * 
 * @param handle                    数据交换层解析器句柄
 * @param power_on                  true: 开机; false: 关机
 */
void dx_handle_update_power_state(struct s_dx_handle *dx_handle, bool power_on);

/**
 * @brief 更新PTT状态
 * 
 * @param handle                    数据交换层解析器句柄
 * @param ptt_state                 PTT状态
 */
void dx_handle_update_ptt_state(struct s_dx_handle *dx_handle, dx_ptt_state_t ptt_state);

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif
