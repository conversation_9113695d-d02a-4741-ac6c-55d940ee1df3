/******************************************************************************
  
                                Includes     
  
 ******************************************************************************/

//============================> System Headers <============================
#include <stdint.h>
#include <string.h>
#include <stdbool.h>

//============================> Libraries Headers <============================
#include "dx.h"

//============================> Project Headers <============================
#include "ui.h"
#include "dx_handle.h"
#include "app.h"

/******************************************************************************
  
                                Defines
  
 ******************************************************************************/

/******************************************************************************
  
                                Structs
  
 ******************************************************************************/

/******************************************************************************
  
                                Defines     
  
 ******************************************************************************/

// 状态上报定时器周期，单位ms
#define STATUS_REPORT_TIMER_PERIOD               500

// 状态更新定时器周期，单位ms
#define STATUS_UPDATE_TIMER_PERIOD               100

// 语音通话状态上报定时器周期，单位ms
#define VOICE_CALL_STATUS_REPORT_TIMER_PERIOD    1000

// 重传定时器周期，单位ms
#define DX_RTX_TIMER_PERIOD                      1000


#define DX_HANDLE_RX_QUEUE_SIZE                  10
#define DX_HANDLE_RX_QUEUE_ITEM_SIZE             80

/******************************************************************************
  
                                Prototypes
  
 ******************************************************************************/

static void proto_handle_init_cb(proto_handle_t *handle, const proto_handle_class_t *class_p) _UI_UNUSED_;
static void proto_handle_deinit_cb(proto_handle_t *handle, const proto_handle_class_t *class_p) _UI_UNUSED_;
static int proto_handle_read_into_buff_cb(proto_handle_t *handle, const proto_handle_class_t *class_p, void *data, uint16_t size) _UI_UNUSED_;
static int proto_handle_write_cb(proto_handle_t *handle, const proto_handle_class_t *class_p, const void *data, uint16_t size) _UI_UNUSED_;
static void proto_handle_parse_frame_cb(proto_handle_t *handle, const proto_handle_class_t *class_p, const void *frame) _UI_UNUSED_;
static uint16_t proto_handle_serialize_cb(proto_handle_t *handle, const proto_handle_class_t *class_p, const proto_handle_frame_info_t *frame_info, void *buff, uint16_t size) _UI_UNUSED_;

static void status_report_timer_cb(proto_timer_t *timer) _PROTO_UNUSED_;
static void voice_call_status_report_timer_cb(proto_timer_t *timer) _PROTO_UNUSED_;

static int dx_handle_send_device_info(dx_handle_t *handle) _PROTO_UNUSED_;
static int dx_handle_send_version_info(dx_handle_t *handle) _PROTO_UNUSED_;
static int dx_handle_send_standby_grp_info(dx_handle_t *handle) _PROTO_UNUSED_;
static int dx_handle_send_voice_call_status(dx_handle_t *handle) _PROTO_UNUSED_;

static proto_rtx_timer_t *find_rtx_timer_by_cmd(proto_rtx_monitor_t *rtx_monitor, uint8_t cmd) _PROTO_UNUSED_;

/******************************************************************************
  
                                Variables
  
 ******************************************************************************/

const proto_handle_class_t dx_handle_class = 
{
    .base_class = &proto_handle_interface,
    .init_cb = proto_handle_init_cb,
    .read_into_buff_cb = proto_handle_read_into_buff_cb,
    .write_cb = proto_handle_write_cb,
    .parse_frame_cb = proto_handle_parse_frame_cb,
    .instance_size = sizeof(dx_handle_t),
};

/******************************************************************************
  
                                Static Functions     
  
 ******************************************************************************/

//============================> Helper Functions <============================

/**
 * @brief 重传完成回调函数
 * 
 * @param rtx_timer                 重传定时器
 */
static void proto_rtx_complete_cb(proto_rtx_timer_t *rtx_timer)
{
    if (rtx_timer->state == PROTO_RTX_FAILED)
    {
        // 若重传失败, 则播放提示音
        ui_controller_play_key_tone(3, 0);
        ui_toast_show_predefined_msg(ui_toast_get_instance(), UI_TOAST_MSG_SYSTEM_NOT_RESPONDING, 0);
    }
}

/**
 * @brief 重传回调函数
 * 
 * @param rtx_timer                 重传定时器
 */
static void proto_rtx_cb(proto_rtx_timer_t *rtx_timer)
{
    uint8_t *data = proto_array_get_data(&rtx_timer->data);
    uint16_t length = proto_array_length(&rtx_timer->data);
    dx_finalize_frame((void *)data, length);
    ui_controller_write_to_data_port(data, length);
}

/**
 * @brief 生成设备信息帧, 并发送
 */
static int dx_handle_send_device_info(dx_handle_t *handle)
{
    uint32_t ms_id = param_get_device_id();
    dx_device_esn_t device_esn = {0};
    param_read_device_esn_into(&device_esn, NULL);

    uint8_t buff[80] = {0};
    int length = dx_generate_device_info(buff, sizeof(buff), ms_id, device_esn.serial_num, device_esn.type, device_esn.manufacturer);
    dx_frame_header_set_frame_num((dx_frame_header_t *)buff, handle->frame_num++);
    return proto_handle_write(&handle->proto_handle, buff, length);
}

/**
 * @brief 生成版本信息帧, 并发送
 */
static int dx_handle_send_version_info(dx_handle_t *handle)
{
    DEVICE_INFORMATION device_info = {0};
    param_read_mc_device_information_into(&device_info, NULL);

    uint8_t buff[80] = {0};
    int length = dx_generate_version_info(buff, sizeof(buff), &device_info, UI_VERSION_MAJOR, UI_VERSION_MINOR, UI_VERSION_PATCH, FONT_VERSION_MAJOR, FONT_VERSION_MINOR, UI_BUILD_YEAR - 2000, UI_BUILD_MONTH, UI_BUILD_DAY);
    dx_frame_header_set_frame_num((dx_frame_header_t *)buff, handle->frame_num++);
    return proto_handle_write(&handle->proto_handle, buff, length);
}

/**
 * @brief 生成守候组信息帧, 并发送
 */
static int dx_handle_send_standby_grp_info(dx_handle_t *handle)
{
    uint8_t buff[80] = {0};
    USER_BOOK user_book;
    ui_page_t *current_page = ui_page_manager_get_current_page();

    if (current_page->page_id != UI_PAGE_ID_HOME)
    {
        param_read_current_contact_into(&user_book);
    }
    else
    {
        ui_home_page_get_standby_group((ui_home_page_t *)current_page, &user_book);
    }

    int length = dx_generate_standby_grp_info(buff, sizeof(buff), user_book.id & 0xFFFFFF, (const char *)user_book.name);
    dx_frame_header_set_frame_num((dx_frame_header_t *)buff, handle->frame_num++);
    return proto_handle_write(&handle->proto_handle, buff, length);
}

/**
 * @brief 将"呼叫模式"转换为"PTT按键ID"
 * 
 * @param call_mode                 呼叫模式
 * @return ui_key_id_t              PTT按键ID
 */
static ui_key_id_t call_mode_to_ptt_key_id(uint8_t call_mode)
{
    return (call_mode == DX_CALL_MODE_CONVENTIONAL) ? UI_KEY_ID_PTT : UI_KEY_ID_PTT_2;
}

/**
 * @brief 处理信息查询
 * 
 * @param handle                    句柄
 * @param frame_info                帧信息
 */
static void dx_handle_process_info_query(dx_handle_t *handle, const dx_frame_info_t *frame_info)
{
    switch (frame_info->header.cmdx)
    {
    case DX_CMD_DEVICE_INFO:
        dx_handle_send_device_info(handle);
        break;
    case DX_CMD_VERSION_INFO:
        dx_handle_send_version_info(handle);
        break;
    case DX_CMD_NETWORK_INFO:
        break;
    case DX_CMD_STANDBY_GRP_INFO:
        dx_handle_send_standby_grp_info(handle);
        break;
    case DX_CMD_POC_USER_INFO:
        dx_handle_send_poc_user_info(handle);
        break;
    case DX_CMD_POC_SERVER_INFO:
        dx_handle_send_poc_server_info(handle);
        break;
    case DX_CMD_POC_APN_INFO:
        dx_handle_send_poc_apn_info(handle);
        break;

    default:
        break;
    }
}

/**
 * @brief 处理呼叫守候组
 * 
 * @param handle                    句柄
 * @param frame_info                帧信息
 */
static void dx_handle_process_call_standby_grp(dx_handle_t *handle, const dx_frame_info_t *frame_info)
{
    uint32_t vs = param_get_latest_stack_vs();

    // 若设备正处于呼叫状态, 则不响应"呼叫守候组"信令
    if (protostack_vs_is_calling(vs))
    {
        return;
    }

    dx_call_standby_grp_dsc_t *call_standby_grp_dsc = (dx_call_standby_grp_dsc_t *)frame_info->data;
    // 将"呼叫模式"转换为"PTT按键ID"
    ui_key_id_t key_id = call_mode_to_ptt_key_id(call_standby_grp_dsc->mode);
    uint8_t work_mode = param_get_work_mode();

    if (work_mode == PARAM_WORK_MODE_DIGITAL_CONVENTIONAL)
    {
        ui_ptt_event_t ptt_event;
        ui_utils_ptt_event_init(&ptt_event, key_id, UI_KEY_PRESSED, UI_PTT_EVENT_TYPE_DEFAULT, NULL);
        ui_controller_notify_key_event(&ptt_event.key_event);
    }
    else if (work_mode == PARAM_WORK_MODE_DIGITAL_TRUNKING)
    {
        ui_ptt_event_t ptt_event;
        ui_utils_ptt_event_init(&ptt_event, key_id, UI_KEY_PRESSED, UI_PTT_EVENT_TYPE_DEFAULT, NULL);
        ui_controller_notify_key_event(&ptt_event.key_event);
    }
    else if (work_mode == PARAM_WORK_MODE_V6)
    {
        ui_ptt_event_t ptt_event;
        ui_utils_ptt_event_init(&ptt_event, key_id, UI_KEY_PRESSED, UI_PTT_EVENT_TYPE_DEFAULT, NULL);
        ui_controller_notify_key_event(&ptt_event.key_event);
    }
}

/**
 * @brief 处理呼叫响应
 * 
 * @param handle                    句柄
 * @param frame_info                帧信息
 */
static void dx_handle_process_call_resp(dx_handle_t *handle, const dx_frame_info_t *frame_info)
{
    dx_call_resp_dsc_t *call_resp_dsc = (dx_call_resp_dsc_t *)frame_info->data;
    proto_rtx_timer_t *rtx_timer = find_rtx_timer_by_cmd(&handle->rtx_monitor, DX_CMD_CALL_REQ);

    // 若存在重传定时器, 则删除以停止重传
    if (rtx_timer != NULL)
    {
        proto_rtx_monitor_delete_timer(&handle->rtx_monitor, rtx_timer->id);
    }

    // 若呼叫建立失败
    if (call_resp_dsc->status == DX_CALL_RESP_REJECTED)
    {
        // 若呼叫建立失败，则播放拒绝提示音
        ui_controller_play_key_tone(3, 0);

        // 在弹窗中显示"呼叫被拒绝"提示
        ui_toast_show_predefined_msg(ui_toast_get_instance(), UI_TOAST_MSG_CALL_REJECTED, 0);

        // 在POC调试助手中更新"呼叫失败"状态
        if (debug_assist_auto_call_enabled())
        {
            debug_assist_update_status_call_success(false);
        }
    }
    // 若呼叫建立成功
    else if (call_resp_dsc->status == DX_CALL_RESP_ACCEPTED)
    {
        // 若呼叫建立成功，则播放呼叫成功提示音
        ui_controller_play_key_tone(2, 0);

        // 更新通话状态、语音呼叫选项、主叫方ID、被叫方ID等信息
        param_update_status_trx_state(PARAM_TRX_STATE_CALLING);
        param_update_status_voice_call_options(call_resp_dsc->so.bit.ig, call_resp_dsc->so.bit.emg, call_resp_dsc->so.bit.bcast, 
                                               call_resp_dsc->so.bit.e2ee, call_resp_dsc->so.bit.all, true);
        param_update_status_caller_id(call_resp_dsc->caller_id);
        param_update_status_callee_id(call_resp_dsc->callee_id);
        param_update_status_call_time(call_resp_dsc->call_time);

        // 若PTT按键仍然处于按下状态, 则发送PTT话权请求
        if (handle->ptt_state == DX_PTT_PRESS)
        {
            dx_handle_send_ptt_auth_req(handle, DX_PTT_PRESS);
        }

        ui_controller_mc_status_updated_handler();

        if (debug_assist_auto_call_enabled())
        {
            debug_assist_update_status_call_success(true);
        }
    }
}

/**
 * @brief 处理PTT话权请求响应
 * 
 * @param handle                    句柄
 * @param frame_info                帧信息
 */
static void dx_handle_process_ptt_auth_resp(dx_handle_t *handle, const dx_frame_info_t *frame_info)
{
    dx_ptt_auth_resp_dsc_t *ptt_auth_resp_dsc = (dx_ptt_auth_resp_dsc_t *)frame_info->data;

    // 若存在重传定时器, 则删除以停止重传
    proto_rtx_timer_t *rtx_timer = find_rtx_timer_by_cmd(&handle->rtx_monitor, DX_CMD_PTT_AUTH_REQ);
    if (rtx_timer != NULL)
    {
        proto_rtx_monitor_delete_timer(&handle->rtx_monitor, rtx_timer->id);
    }

    if (ptt_auth_resp_dsc->status == DX_PTT_AUTH_STATUS_REJECTED)
    {
        // 若话权请求失败，则播放拒绝提示音
        ui_controller_play_key_tone(3, 0);

        // 在弹窗中显示"话权被拒绝"提示
        ui_toast_show_predefined_msg(ui_toast_get_instance(), UI_TOAST_MSG_PTT_AUTH_REJECTED, 0);

        // 关闭MIC
        ui_controller_toggle_mic(UI_DISABLED);

        if (debug_assist_auto_call_enabled())
        {
            debug_assist_update_status_ptt_granted(false);
        }
    }
    else if (ptt_auth_resp_dsc->status == DX_PTT_AUTH_STATUS_ACCEPTED)
    {
        param_update_status_trx_state(PARAM_TRX_STATE_SPEAKING);

        // 打开MIC
        ui_controller_toggle_voice(UI_DISABLED);
        ui_controller_toggle_mic(UI_ENABLED);

        ui_controller_mc_status_updated_handler();

        if (debug_assist_auto_call_enabled())
        {
            debug_assist_update_status_ptt_granted(true);
        }
    }
}

static void dx_handle_process_4g_standby_status(dx_handle_t *handle, const dx_frame_info_t *frame_info)
{
    dx_4g_standby_status_dsc_t *standby_status_dsc = (dx_4g_standby_status_dsc_t *)frame_info->data;

    // 获取当前的OTA状态和新的OTA状态
    param_ota_state_t current_ota_state = param_get_ota_state();
    param_ota_state_t new_ota_state = PARAM_OTA_IDLE;

    if (standby_status_dsc->status.bit.ota)
    {
        new_ota_state = PARAM_OTA_SYSTEM_UPGRADING;
    }
    else if (standby_status_dsc->status.bit.program)
    {
        new_ota_state = PARAM_OTA_PARAM_UPDATING;
    }

    // 更新POC待机状态
    param_set_poc_standby_status(standby_status_dsc->status.raw);
    param_set_poc_reg_status(standby_status_dsc->reg_status);

    // dx_4g_standby_status_t standby_status = { .raw = standby_status_dsc->status.raw };
    // LOG_DEBUG("Sim Ready: %d, Network OK: %d, Registered: %d, IPv4: %x, Running: %d, DX Sync: %d, MSVR Conn: %d, PoC Svr OK: %d",
    //           standby_status.bit.sim_ready, standby_status.bit.network_ok, standby_status.bit.registered, 
    //           standby_status.bit.ipv4, standby_status.bit.running, standby_status.bit.dx_sync, 
    //           standby_status.bit.msvr_conn, standby_status.bit.pocsvr_ok);

    // 更新OTA状态和OTA进度
    param_set_poc_ota_state(new_ota_state);
    param_set_poc_ota_progress(standby_status_dsc->progress);

    // 更新4G模块接收场强
    param_set_poc_csq(standby_status_dsc->csq);
    
    if (current_ota_state == PARAM_OTA_IDLE && new_ota_state != PARAM_OTA_IDLE)
    {
        ui_page_exit_param_t exit_param;
        ui_page_exit_param_init(&exit_param, UI_ENTER_NEW_PAGE);
        ui_page_manager_jump_to_page(UI_PAGE_ID_UPGRADE, &exit_param, NULL);
    }
}

static void dx_handle_process_4g_notify(dx_handle_t *handle, const dx_frame_info_t *frame_info)
{
    s_dx_4g_notify_dsc_t *dx_4g_notify = (s_dx_4g_notify_dsc_t *)frame_info->data;

    if (dx_4g_notify->t == DX_4G_NOTIFY_VER)
    {
        param_set_poc_version(dx_4g_notify->info.ver.sw_major, dx_4g_notify->info.ver.sw_minor, dx_4g_notify->info.ver.sw_patch);
    }
    else if (dx_4g_notify->t == DX_4G_NOTIFY_NETWORK)
    {
        if (dx_4g_notify->info.network.ipv4_valid)
        {
            uint32_t ipv4; 
            memmove(&ipv4, dx_4g_notify->info.network.ipv4, sizeof(uint32_t));
            param_set_poc_server_ipv4(ipv4);
        }
        else
        {
            param_set_poc_server_ipv4(0);
        }
    }
    else if (dx_4g_notify->t == DX_4G_NOTIFY_SIGNAL)
    {
        param_set_poc_csq(dx_4g_notify->info.signal.csq);
    }
    else if (dx_4g_notify->t == DX_4G_NOTIFY_OTA)
    {
        param_set_poc_ota_progress(dx_4g_notify->info.ota.progress);
    }
}

/**
 * @brief 处理语音通话状态
 * 
 * @param handle                    句柄
 * @param frame_info                帧信息
 */
static void dx_handle_process_voice_call_status(dx_handle_t *handle, const dx_frame_info_t *frame_info)
{
    dx_voice_call_status_dsc_t *voice_call_status_dsc = (dx_voice_call_status_dsc_t *)frame_info->data;

    // 获取当前通话状态, 并更新通话状态
    param_trx_state_t current_trx_state = param_get_trx_state();
    uint8_t new_trx_state = voice_call_status_dsc->state;
    param_update_status_trx_state(new_trx_state);

    // 若当前处于空闲状态
    if (new_trx_state == DX_TRX_STATE_IDLE)
    {
        // 若刚刚退出通话状态
        if (current_trx_state != DX_TRX_STATE_IDLE)
        {
            param_mutex_lock();

            // 若先前处于放音状态
            if (current_trx_state == DX_TRX_STATE_LISTENING)
            {
                // 关闭话音
                ui_controller_toggle_voice(UI_DISABLED);
            }
            // 若先前处于讲话状态
            else if (current_trx_state == DX_TRX_STATE_SPEAKING)
            {
                // 关闭MIC
                ui_controller_toggle_mic(UI_DISABLED);
            }

            // 清空PCMA缓冲区
            ui_controller_clear_pcma_audio_buffer();

            param_mutex_unlock();

            // 通知主控使用话音音量参数播放话音
            ui_controller_adjust_alert_volume(param_get_tip_self());

            // 播放通话结束提示音
            ui_controller_play_key_tone(4, 0);
        }
    }
    // 若当前处于通话状态
    else 
    {
        // 更新相关通话状态
        param_update_status_voice_call_options(voice_call_status_dsc->so.bit.ig, voice_call_status_dsc->so.bit.emg, voice_call_status_dsc->so.bit.bcast, 
                                               voice_call_status_dsc->so.bit.e2ee, voice_call_status_dsc->so.bit.all, voice_call_status_dsc->so.bit.caller);
    
        param_update_status_caller_id(voice_call_status_dsc->caller_id);
        param_update_status_callee_id(voice_call_status_dsc->callee_id);
        param_update_status_speaker_id(voice_call_status_dsc->speaker_id);
        param_update_status_call_time(voice_call_status_dsc->call_time);

        // 若当前处于通话(无人讲话)状态
        if (new_trx_state == DX_TRX_STATE_CALLING)
        {
            param_mutex_lock();

            // 若先前为放音状态
            if (current_trx_state == DX_TRX_STATE_LISTENING)
            {
                // 关闭话音
                ui_controller_toggle_voice(UI_DISABLED);
            }
            // 若先前为讲话状态
            else if (current_trx_state == DX_TRX_STATE_SPEAKING)
            {
                // 关闭MIC
                ui_controller_toggle_mic(UI_DISABLED);
            }

            // 清空PCMA缓冲区
            ui_controller_clear_pcma_audio_buffer();

            param_mutex_unlock();
        }
        else if (new_trx_state == DX_TRX_STATE_LISTENING)
        {
            // 若刚刚进入放音状态
            if (current_trx_state != DX_TRX_STATE_LISTENING)
            {
                // 通知主控使用话音音量参数播放话音
                ui_controller_adjust_voice_volume(param_get_sound_gain());

                // 打开话音
                ui_controller_toggle_mic(UI_DISABLED);
                ui_controller_toggle_voice(UI_ENABLED);
            }
        }
        else if (new_trx_state == DX_TRX_STATE_SPEAKING)
        {
            // 若刚刚进入讲话状态
            if (current_trx_state != DX_TRX_STATE_SPEAKING)
            {
                // 打开麦克风
                ui_controller_toggle_voice(UI_DISABLED);
                ui_controller_toggle_mic(UI_ENABLED);
            }
        }
    }

    ui_controller_mc_status_updated_handler();
}

//============================> Class Methods <============================

static void proto_handle_init_cb(proto_handle_t *handle, const proto_handle_class_t *class_p)
{
    dx_handle_t *dx_handle = (dx_handle_t *)handle;

    proto_queue_init(&handle->rx_queue, DX_HANDLE_RX_QUEUE_SIZE, DX_HANDLE_RX_QUEUE_ITEM_SIZE);

    // 初始化重传定时器监视器
    proto_rtx_monitor_init(&dx_handle->rtx_monitor);

    // 初始化状态信息
    dx_handle->frame_num = 0;

    // 初始化定时器(若设备不处于POC模式, 则暂停状态上报定时器)
    dx_handle->status_report_timer = proto_timer_create(status_report_timer_cb, STATUS_REPORT_TIMER_PERIOD, -1, dx_handle);
    
    if (param_get_work_mode() != PARAM_WORK_MODE_POC)
    {
        proto_timer_pause(dx_handle->status_report_timer);
    }
    else
    {
        proto_timer_resume(dx_handle->status_report_timer);
    }

    // 发送"上电"状态帧
    dx_handle_update_power_state(dx_handle, true);
}

static int proto_handle_read_into_buff_cb(proto_handle_t *handle, const proto_handle_class_t *class_p, void *data, uint16_t size)
{
    dx_frame_header_t *frame_header = (dx_frame_header_t *)data;

    // 检查同步字
    if (frame_header->sync != DX_SYNC_PATTERN)
    {
        return DX_ERR_SYNC_INVALID;
    }

    proto_queue_push_back(&handle->rx_queue, data);

    return frame_header->length;
}

static int proto_handle_write_cb(proto_handle_t *handle, const proto_handle_class_t *class_p, const void *data, uint16_t size)
{
    dx_finalize_frame((void *)data, size);
    ui_controller_write_to_data_port((uint8_t *)data, size);
    return size;
}

static void proto_handle_parse_frame_cb(proto_handle_t *handle, const proto_handle_class_t *class_p, const void *frame)
{
    dx_frame_header_t *frame_header = (dx_frame_header_t *)frame;
    dx_handle_t *dx_handle = (dx_handle_t *)handle;

    // 解析协议交换层数据帧
    dx_frame_info_t dx_frame_info;
    int parse_result = dx_parse_frame_info(frame, frame_header->length, &dx_frame_info);

    if (parse_result == DX_OK)
    {
        // 更新状态"4G模块在线"
        param_set_poc_module_online(true);

        // 若数据为ACK帧
        if (dx_frame_info.header.type.bit.type == DX_FRAME_TYPE_ACK)
        {
            proto_rtx_monitor_delete_timer(&dx_handle->rtx_monitor, dx_frame_info.header.frame_num);
        }
        else
        {
            // 若该数据帧需要ACK, 则发送ACK帧
            if (dx_frame_info.header.type.bit.ack_required)
            {
                uint8_t buff[80] = {0};
                int length = dx_generate_ack(buff, sizeof(buff), dx_frame_info.header.frame_num, dx_frame_info.header.cmd, dx_frame_info.header.cmdx);
                proto_handle_write(handle, buff, length);
            }

            switch (dx_frame_info.header.cmd)
            {
            case DX_CMD_INFO_QUERY:
                dx_handle_process_info_query(dx_handle, &dx_frame_info);
                break;

            case DX_CMD_CALL_STANDBY_GRP:
                dx_handle_process_call_standby_grp(dx_handle, &dx_frame_info);
                break;

            case DX_CMD_CALL_RESP:
                dx_handle_process_call_resp(dx_handle, &dx_frame_info);
                break;

            case DX_CMD_VOICE_CALL_STATUS:
                dx_handle_process_voice_call_status(dx_handle, &dx_frame_info);
                break;

            case DX_CMD_PTT_AUTH_RESP:
                dx_handle_process_ptt_auth_resp(dx_handle, &dx_frame_info);
                break;

            case DX_CMD_4G_STANDBY_STATUS:
                dx_handle_process_4g_standby_status(dx_handle, &dx_frame_info);
                break;

            case DX_CMD_4G_NOTIFY:
                dx_handle_process_4g_notify(dx_handle, &dx_frame_info);
                break;

            default:
                break;
            }
        }
    }
    else
    {
        LOG_ERROR("Parse frame failed, Error code: %d", parse_result);
    }
}

/**
 * @brief 给定一个交互层数据帧的命令字, 查找是否有正在重发该数据帧的定时器
 * 
 * @param rtx_monitor               重传定时器监视器
 * @param cmd                       命令字
 * 
 * @return proto_rtx_timer_t*       指向正在重发该数据帧的定时器的指针, 如果没有找到, 则返回NULL
 */
static proto_rtx_timer_t *find_rtx_timer_by_cmd(proto_rtx_monitor_t *rtx_monitor, uint8_t cmd)
{
    proto_rtx_timer_t *rtx_timer;

    PROTO_LL_READ(&rtx_monitor->rtx_timer_ll, rtx_timer)
    {
        dx_frame_header_t *frame_header = proto_array_get_data(&rtx_timer->data);

        if (frame_header->cmd == cmd)
        {
            return rtx_timer;
        }
    }

    return NULL;
}

//============================> Timer/Async Callbacks <============================

static void status_report_timer_cb(proto_timer_t *timer)
{
    static uint16_t cnt = 0;
    dx_handle_t *dx_handle = (dx_handle_t *)proto_timer_get_user_data(timer);

    //============================> 500ms状态上报 <============================

    // 待机状态
    uint8_t buff[80] = {0};
    int len;

    // 1秒
    if (cnt % 2 == 0)
    {
        dx_standby_status_t standby_status = { .raw = param_get_standby_status() };
        dx_gps_info_t gps_info = {0};
        gps_info.gps_state.raw = param_get_gps_state();
        gps_info.latitude = param_get_latitude();
        gps_info.longitude = param_get_longitude();
        gps_info.speed = param_get_speed();

        USER_BOOK user_book;
        ui_page_t *current_page = ui_page_manager_get_current_page();

        if (current_page->page_id != UI_PAGE_ID_HOME)
        {
            param_read_current_contact_into(&user_book);
        }
        else
        {
            ui_home_page_get_standby_group((ui_home_page_t *)current_page, &user_book);
        }

        len = dx_generate_standby_status(buff, sizeof(buff), standby_status.raw, param_get_rssi(), 0,
                                         param_get_battery_level(), user_book.id & 0xFFFFFF, (const char *)user_book.name, &gps_info);
        dx_frame_header_set_frame_num((dx_frame_header_t *)buff, dx_handle->frame_num++);
        proto_handle_write(&dx_handle->proto_handle, buff, len);
    }

    // 5秒
    if (cnt % 10 == 0)
    {
        cnt = 0;
    }
    
    cnt++;
}

static void voice_call_status_report_timer_cb(proto_timer_t *timer)
{
    // dx_handle_t *dx_handle = (dx_handle_t *)proto_timer_get_user_data(timer);
    // uint8_t buff[80];
    // int length = 0;

    // // 生成语音通话状态帧
    // if (dx_handle->trx_state == DX_TRX_STATE_CALLING)
    // {
    //     length = dx_generate_voice_call_status(buff, sizeof(buff), &dx_handle->so_voice, dx_handle->call_time, 
    //                                            dx_handle->trx_state, dx_handle->calling_party_id, dx_handle->called_party_id, 0);
    // }
    // else
    // {
    //     length = dx_generate_voice_call_status(buff, sizeof(buff), &dx_handle->so_voice, dx_handle->call_time, 
    //                                            dx_handle->trx_state, dx_handle->calling_party_id, dx_handle->called_party_id, dx_handle->speaker_id);
    // }

    // // 发送语音通话状态帧
    // proto_handle_write_cb(&dx_handle->proto_handle, &dx_handle_class, buff, length);
}


/******************************************************************************
  
                                Public Functions     
  
 ******************************************************************************/

proto_handle_t *dx_handle_create(void)
{
    proto_handle_t *new_handle = proto_handle_class_create(&dx_handle_class);
    proto_handle_init(new_handle);
    return new_handle;
}

dx_handle_t *dx_handle_get_instance(void)
{
    static proto_handle_t *instance = NULL;

    if (instance == NULL)
    {
        instance = dx_handle_create();
    }

    return (dx_handle_t *)instance;
}

void dx_handle_toggle_status_report_timer(struct s_dx_handle *dx_handle, bool enable)
{
    if (dx_handle == NULL) return;

    if (enable)
    {
        proto_timer_resume(dx_handle->status_report_timer);
        proto_timer_ready(dx_handle->status_report_timer);
    }
    else
    {
        proto_timer_pause(dx_handle->status_report_timer);
    }
}

void dx_handle_toggle_voice_call_status_report_timer(dx_handle_t *dx_handle, bool enable)
{
    if (dx_handle == NULL) return;

    if (enable)
    {
        proto_timer_resume(dx_handle->voice_call_status_report_timer);
    }
    else
    {
        proto_timer_pause(dx_handle->voice_call_status_report_timer);
    }
}

void dx_handle_send_call_req(struct s_dx_handle *dx_handle, bool ig, bool emg, uint32_t callee_id)
{
    if (dx_handle == NULL) return;

    dx_so_voice_t so_voice = 
    {
        .bit.pl = param_get_priority(),
        .bit.ig = ig,
        .bit.emg = emg,
        .bit.caller = true,
    };

    uint8_t buff[80] = {0};
    int len = dx_generate_call_req(buff, sizeof(buff), so_voice.raw, callee_id, param_get_device_id());
    dx_frame_header_set_ack_required((dx_frame_header_t *)buff, true);
    dx_frame_header_set_frame_num((dx_frame_header_t *)buff, dx_handle->frame_num);
    proto_handle_write(&dx_handle->proto_handle, buff, len);

    if (find_rtx_timer_by_cmd(&dx_handle->rtx_monitor, DX_CMD_CALL_REQ) == NULL)
    {
        proto_rtx_monitor_create_timer(&dx_handle->rtx_monitor, dx_handle->frame_num, DX_RTX_TIMER_PERIOD, 3, buff, len, proto_rtx_cb, proto_rtx_complete_cb);
    }

    dx_handle->frame_num++;
}

void dx_handle_send_ptt_auth_req(struct s_dx_handle *dx_handle, dx_ptt_state_t ptt_state)
{
    if (dx_handle == NULL) return;

    dx_so_voice_t so_voice = { .raw = param_get_voice_call_options() };

    uint8_t buff[80] = {0};
    int len = dx_generate_ptt_auth_req(buff, sizeof(buff), so_voice.raw, ptt_state, param_get_callee_id(), param_get_caller_id(), param_get_device_id());
    dx_frame_header_set_ack_required((dx_frame_header_t *)buff, true);
    dx_frame_header_set_frame_num((dx_frame_header_t *)buff, dx_handle->frame_num);
    proto_handle_write(&dx_handle->proto_handle, buff, len);

    if (find_rtx_timer_by_cmd(&dx_handle->rtx_monitor, DX_CMD_PTT_AUTH_REQ) == NULL)
    {
        proto_rtx_monitor_create_timer(&dx_handle->rtx_monitor, dx_handle->frame_num, DX_RTX_TIMER_PERIOD, 3, buff, len, proto_rtx_cb, proto_rtx_complete_cb);
    }

    dx_handle->frame_num++;
}

void dx_handle_send_call_exit_req(struct s_dx_handle *dx_handle)
{
    if (dx_handle == NULL) return;

    dx_so_voice_t so_voice = { .raw = param_get_voice_call_options() };
    uint8_t buff[80] = {0};
    int len = dx_generate_call_exit_req(buff, sizeof(buff), so_voice.raw, param_get_callee_id(), param_get_caller_id());
    dx_frame_header_set_ack_required((dx_frame_header_t *)buff, true);
    dx_frame_header_set_frame_num((dx_frame_header_t *)buff, dx_handle->frame_num);
    proto_handle_write(&dx_handle->proto_handle, buff, len);

    if (find_rtx_timer_by_cmd(&dx_handle->rtx_monitor, DX_CMD_CALL_EXIT_REQ) == NULL)
    {
        proto_rtx_monitor_create_timer(&dx_handle->rtx_monitor, dx_handle->frame_num, DX_RTX_TIMER_PERIOD, 3, buff, len, proto_rtx_cb, proto_rtx_complete_cb);
    }

    dx_handle->frame_num++;
}

int dx_handle_send_poc_user_info(dx_handle_t *handle)
{
    uint8_t buff[80] = {0};
    int length = dx_generate_poc_user_info(buff, sizeof(buff), param_get_poc_username(), param_get_poc_password());
    dx_frame_header_set_frame_num((dx_frame_header_t *)buff, handle->frame_num++);
    return proto_handle_write(&handle->proto_handle, buff, length);
}

int dx_handle_send_poc_server_info(dx_handle_t *handle)
{
    uint8_t buff[80] = {0};
    char *server_addr = param_get_poc_server_addr();
    uint16_t server_port = param_get_poc_server_port();
    LOG_DEBUG("Server addr: %s, Server port: %d", server_addr, server_port);
    int length = dx_generate_poc_server_info(buff, sizeof(buff), server_addr, server_port);
    dx_frame_header_set_frame_num((dx_frame_header_t *)buff, handle->frame_num++);
    return proto_handle_write(&handle->proto_handle, buff, length);
}

int dx_handle_send_poc_apn_info(dx_handle_t *handle)
{
    uint8_t buff[80] = {0};
    int length = dx_generate_poc_apn_info(buff, sizeof(buff), param_get_poc_activation_type(), param_get_poc_apn(), param_get_poc_auth_type());
    dx_frame_header_set_frame_num((dx_frame_header_t *)buff, handle->frame_num++);
    return proto_handle_write(&handle->proto_handle, buff, length);
}

void dx_handle_update_power_state(struct s_dx_handle *dx_handle, bool power_on)
{
    if (dx_handle == NULL) return;

    dx_standby_status_t standby_status = { .raw = param_get_standby_status() };

    if (power_on)
    {
        standby_status.bit.powering_on = 1;
    }
    else
    {
        standby_status.bit.powering_off = 1;
    }

    int16_t rssi = param_get_rssi();
    dx_gps_info_t gps_info = {0};
    gps_info.gps_state.raw = param_get_gps_state();
    gps_info.latitude = param_get_latitude();
    gps_info.longitude = param_get_longitude();
    gps_info.speed = param_get_speed();

    USER_BOOK user_book;
    ui_page_t *current_page = ui_page_manager_get_current_page();

    if (current_page->page_id != UI_PAGE_ID_HOME)
    {
        param_read_current_contact_into(&user_book);
    }
    else
    {
        ui_home_page_get_standby_group((ui_home_page_t *)current_page, &user_book);
    }

    uint8_t buff[80] = {0};
    int len = dx_generate_standby_status(buff, sizeof(buff), standby_status.raw, dx_dbm_to_rssi(rssi), 0, 
                                         param_get_battery_level(), user_book.id & 0xFFFFFF, (const char *)user_book.name, &gps_info);
    dx_frame_header_set_frame_num((dx_frame_header_t *)buff, dx_handle->frame_num++);
    proto_handle_write(&dx_handle->proto_handle, buff, len);
}

void dx_handle_update_ptt_state(struct s_dx_handle *dx_handle, dx_ptt_state_t ptt_state)
{
    if (dx_handle == NULL) return;

    dx_handle->ptt_state = ptt_state;
}

