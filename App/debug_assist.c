/******************************************************************************
  
                                Includes     
  
 ******************************************************************************/

//============================> System Headers <============================
#include <stdint.h>
#include <string.h>
#include <stdbool.h>

//============================> Libraries Headers <============================
#include "ui.h"

//============================> Project Headers <============================
#include "debug_assist.h"

/******************************************************************************
  
                                Defines     
  
 ******************************************************************************/

/**
 * @brief 按下PTT键后, 会等待一段时间后松开PTT键。
 * 
 * @note 在这段时间内, 我们可记录是否从POC端接收了呼叫成功的消息, 以及PTT授权成功的消息。
 */
#define AUTO_CALL_PTT_PRESSED_PERIOD                   30000 // 自动呼叫调试中, 按下PTT的时长
#define AUTO_CALL_PTT_RELEASED_PERIOD                  2000 // 自动呼叫调试中, 松开PTT的时长

/**
 * @brief 
 */
#define AUTO_CALL_CANCELED_PERIOD                      1000 // 自动呼叫调试中, 拆线的时长
#define AUTO_CALL_STATE_IDLE_PERIOD                    1000 // 自动呼叫调试中, 空闲状态的时长

/******************************************************************************
  
                                Prototypes
  
 ******************************************************************************/

/******************************************************************************
  
                                Variables
  
 ******************************************************************************/

static debug_assist_t debug_assist;

/******************************************************************************
  
                                Static Functions     
  
 ******************************************************************************/

//============================> Helper Functions <============================

//============================> Timer Callbacks <============================

static void auto_call_timer_cb(proto_timer_t *timer)
{
    param_trx_state_t trx_state = param_get_trx_state();

    switch (debug_assist.auto_call_state)
    {
    case DA_AUTO_CALL_STATE_PTT_PRESSED:
    {
        // 模拟松开PTT键, 并将PTT事件通知给主控以结束呼叫
        ui_ptt_event_t ptt_event;
        ui_utils_ptt_event_init(&ptt_event, UI_KEY_ID_PTT, UI_KEY_RELEASED, UI_PTT_EVENT_TYPE_DEFAULT, NULL);
        ui_controller_notify_key_event(&ptt_event.key_event);

        do
        {
            // 等待`AUTO_CALL_PTT_RELEASED_PERIOD`时间后, 若未收到呼叫成功消息, 则认为呼叫失败
            if (debug_assist.pending_call)
            {
                debug_assist.pending_call = false;
                debug_assist.call_success = false;
                debug_assist.ptt_granted = false;
                
                proto_timer_set_period(debug_assist.auto_call_timer, AUTO_CALL_STATE_IDLE_PERIOD);

                debug_assist.auto_call_state = DA_AUTO_CALL_STATE_IDLE;
                debug_assist.call_statistic.call_fail_count++;

                break;
            }

            if (debug_assist.call_success)
            {
                debug_assist.call_statistic.call_success_count++;

                if (debug_assist.ptt_granted)
                {
                    // 等待`AUTO_CALL_PTT_RELEASED_PERIOD`时间后
                    proto_timer_set_period(debug_assist.auto_call_timer, AUTO_CALL_PTT_RELEASED_PERIOD);

                    debug_assist.auto_call_state = DA_AUTO_CALL_STATE_PTT_RELEASED;
                    debug_assist.call_statistic.ptt_granted_count++;
                }
                else
                {
                    // 模拟按下拆线键
                    ui_key_event_t key_event;
                    ui_utils_key_event_init(&key_event, UI_KEY_ID_CANCEL, UI_KEY_PRESSED);
                    ui_controller_notify_key_event(&key_event);

                    // 模拟松开拆线键
                    ui_utils_key_event_init(&key_event, UI_KEY_ID_CANCEL, UI_KEY_RELEASED);
                    ui_controller_notify_key_event(&key_event);

                    proto_timer_set_period(debug_assist.auto_call_timer, DA_AUTO_CALL_STATE_CANCELED);
                    debug_assist.auto_call_state = DA_AUTO_CALL_STATE_CANCELED;
                }
            }
            else
            {
                proto_timer_set_period(debug_assist.auto_call_timer, AUTO_CALL_STATE_IDLE_PERIOD);

                debug_assist.auto_call_state = DA_AUTO_CALL_STATE_IDLE;
                debug_assist.call_statistic.call_fail_count++;
            }
        } while (0);

        debug_assist.call_success = false;
        debug_assist.ptt_granted = false;
    }
    break;
    case DA_AUTO_CALL_STATE_PTT_RELEASED:
    {
        // 模拟按下拆线键
        ui_key_event_t key_event;
        ui_utils_key_event_init(&key_event, UI_KEY_ID_CANCEL, UI_KEY_PRESSED);
        ui_controller_notify_key_event(&key_event);

        // 模拟松开拆线键
        ui_utils_key_event_init(&key_event, UI_KEY_ID_CANCEL, UI_KEY_RELEASED);
        ui_controller_notify_key_event(&key_event);

        proto_timer_set_period(debug_assist.auto_call_timer, DA_AUTO_CALL_STATE_CANCELED);
        debug_assist.auto_call_state = DA_AUTO_CALL_STATE_CANCELED;
    }
    break;
    case DA_AUTO_CALL_STATE_CANCELED:
    {
        proto_timer_set_period(debug_assist.auto_call_timer, AUTO_CALL_STATE_IDLE_PERIOD);
        debug_assist.auto_call_state = DA_AUTO_CALL_STATE_IDLE;
    }
    break;
    case DA_AUTO_CALL_STATE_IDLE:
    {
        // 当处于空闲状态时, 尝试模拟按下PTT键发起呼叫
        if (trx_state == PARAM_TRX_STATE_IDLE)
        {
            // 模拟按下PTT键, 并将PTT事件通知给主控以发起呼叫
            ui_ptt_event_t ptt_event;
            ui_utils_ptt_event_init(&ptt_event, UI_KEY_ID_PTT, UI_KEY_PRESSED, UI_PTT_EVENT_TYPE_DEFAULT, NULL);
            ui_controller_notify_key_event(&ptt_event.key_event);

            // 等待`AUTO_CALL_PTT_PRESSED_PERIOD`时间后, 检查是否呼叫成功
            proto_timer_set_period(debug_assist.auto_call_timer, AUTO_CALL_PTT_PRESSED_PERIOD);

            // 记录相关统计信息
            debug_assist.auto_call_state = DA_AUTO_CALL_STATE_PTT_PRESSED;
            debug_assist.call_statistic.call_attempt_count++;

            debug_assist.pending_call = true;
            debug_assist.call_success = false;
            debug_assist.ptt_granted = false;
        }
        else
        {
            proto_timer_set_period(debug_assist.auto_call_timer, AUTO_CALL_STATE_IDLE_PERIOD);
        }
    }
    break;
    default:
        break;
    }
}

/******************************************************************************
  
                                Public Functions     
  
 ******************************************************************************/

void debug_assist_toggle_auto_call(bool enable)
{
    if (enable)
    {
        // 启动自动呼叫定时器
        debug_assist.auto_call_timer = proto_timer_create(auto_call_timer_cb, AUTO_CALL_PTT_PRESSED_PERIOD, -1, NULL);
        debug_assist.auto_call_state = DA_AUTO_CALL_STATE_IDLE;
        proto_timer_ready(debug_assist.auto_call_timer);

        debug_assist.auto_call_enabled = true;
    }
    else
    {
        // 停止自动呼叫定时器
        proto_timer_delete(debug_assist.auto_call_timer);
        debug_assist.auto_call_state = DA_AUTO_CALL_STATE_IDLE;

        debug_assist.auto_call_enabled = false;
    }
}

bool debug_assist_auto_call_enabled(void)
{
    return debug_assist.auto_call_enabled;
}

void debug_assist_update_status_call_success(bool call_success)
{
    if (debug_assist.pending_call == true)
    {
        debug_assist.pending_call = false;

        if (call_success == false && debug_assist.auto_call_state == DA_AUTO_CALL_STATE_PTT_PRESSED)
        {
            proto_timer_ready(debug_assist.auto_call_timer);
        }

        debug_assist.call_success = call_success;
    }
}

void debug_assist_update_status_ptt_granted(bool ptt_granted)
{
    if (ptt_granted == false && debug_assist.auto_call_state == DA_AUTO_CALL_STATE_PTT_PRESSED)
    {
        proto_timer_ready(debug_assist.auto_call_timer);
    }

    debug_assist.ptt_granted = ptt_granted;
}

void debug_assist_update_status_call_canceled(void)
{
    if (debug_assist.auto_call_state == DA_AUTO_CALL_STATE_PTT_PRESSED)
    {
        proto_timer_ready(debug_assist.auto_call_timer);
    }
}

uint16_t debug_assist_get_call_attempt_count(void)
{
    return debug_assist.call_statistic.call_attempt_count;
}

uint16_t debug_assist_get_call_success_count(void)
{
    return debug_assist.call_statistic.call_success_count;
}

uint16_t debug_assist_get_ptt_granted_count(void)
{
    return debug_assist.call_statistic.ptt_granted_count;
}

uint16_t debug_assist_get_call_fail_count(void)
{
    return debug_assist.call_statistic.call_fail_count;
}
