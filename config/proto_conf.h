/*
 * Copy this file as `proto_conf.h`
 * 1. simply next to the `proto` folder
 * 2. or any other places and
 *    - define `PROTO_CONF_INCLUDE_SIMPLE`
 *    - add the path as include path
 */

 #if 1

 #ifndef PROTO_CONF_H
 #define PROTO_CONF_H
 
 /******************************************************************************
   
                                 Includes     
   
  ******************************************************************************/
 
 /******************************************************************************
   
                                 内存池模块配置   
   
  ******************************************************************************/
 
 #define PROTO_USE_MEM                       1
 #define PROTO_MEM_SIZE                      (8 * 1024)
 
 /******************************************************************************
 
                                 日志模块配置
 
  ******************************************************************************/
 
 /**
  *  启用日志模块 = 1, 禁用日志模块 = 0
  */
 #define PROTO_USE_LOG                       0
 
 #if PROTO_USE_LOG
 
 //============================> 日志输出方式 <============================
 
 #define PROTO_LOG_PRINTF                    1       // Log will be written to standard output(stdout)
 
 //============================> 日志级别 <============================
 
 #define PROTO_LOG_LEVEL                     PROTO_LOG_LEVEL_TRACE
 
 //============================> 日志模块功能 <============================
 
 // 启用/禁用PROTO_LOG_TRACE_XXX，以产生各种类型的日志
 #define PROTO_MEM_TRACE                     0
 
 #endif
 
 /******************************************************************************
 
                                 定时器模块配置
 
  ******************************************************************************/
 
 #define PROTO_USE_TIMER                     1
 
 /******************************************************************************
 
                                 协议处理器模块配置
 
  ******************************************************************************/
 
 #define PROTO_USE_HANDLE_MANAGER            1
 
 /******************************************************************************
 
                                 文本处理模块配置
 
  ******************************************************************************/
 
 #define PROTO_USE_TXT                       0
 
 /******************************************************************************
 
                                 断言
 
  ******************************************************************************/
 
 #define PROTO_USE_ASSERT_NULL               1       // 检查是否为空指针
 #define PROTO_USE_ASSERT_MALLOC             1       // 检查是否分配内存成功
 
 /******************************************************************************
 
                                 MNMEA模块配置
 
  ******************************************************************************/
 
 #define MINMEA_MAX_SENTENCE_LENGTH          82
 
 #endif /* PROTO_CONF_H */
 
 #endif
 