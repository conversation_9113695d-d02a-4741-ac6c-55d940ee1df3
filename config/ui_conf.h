#if 1

#ifndef UI_CONF_H
#define UI_CONF_H

/******************************************************************************
  
                                Includes     
  
 ******************************************************************************/
#include <stdint.h>

/******************************************************************************

                                日志配置

 ******************************************************************************/

//============================> Enable/Disable Logging Module <============================

/**
 *  Enable or disable UI Logging Module
 *  Enable = 1, Disable = 0
 */
#define UI_USE_LOG                          0

//============================> Logging Method <============================
#if UI_USE_LOG
#define UI_LOG_PRINTF                       0       // Log will be written to standard output(stdout)
#define UI_LOG_TO_MC_DEBUG_INTERFACE        1       // Log will be written to the debug interface of master control

//============================> Log Level <============================

#define UI_LOG_LEVEL                        UI_LOG_LEVEL_TRACE
/**
 * How important log should be added:
 * UI_LOG_LEVEL_TRACE       Use this when we want a lot of logs to give detailed information
 * UI_LOG_LEVEL_DEBUG       Use this for debugging purposes.
 * UI_LOG_LEVEL_INFO        Use this when we want to post important information.
 * UI_LOG_LEVEL_WARN        Use this if something unwanted happened, but didn't cause a problem
 * UI_LOG_LEVEL_ERROR       Use this if a critical issue occured that resulted in system's failure
 * UI_LOG_LEVEL_FATAL       Use this to catch errors that should never happen
 * UI_LOG_LEVEL_NONE        Do not log anything
 */

//============================> Logging Module Features <============================

/* Enable or disable UI_LOG_TRACE_XXX that produces various types of logs*/
#define UI_LOG_TRACE_HOME_PAGE              0
#define UI_LOG_TRACE_MENU_PAGE              0
#define UI_LOG_TRACE_ICON                   0
#define UI_LOG_TRACE_IME                    0
#define UI_LOG_TRACE_SOCKET                 0
#define UI_LOG_TRACE_CONTROLLER             0
#define UI_LOG_TRACE_INDEV                  0
#define UI_LOG_TRACE_MAG                    0
#define UI_LOG_TRACE_ACCELMETER             0

#endif

/******************************************************************************
  
                                屏幕配置
  
 ******************************************************************************/

#define HAL_BRIGHTNESS_LVL_1                4
#define HAL_BRIGHTNESS_LVL_2                25
#define HAL_BRIGHTNESS_LVL_3                50
#define HAL_BRIGHTNESS_LVL_4                70
#define HAL_BRIGHTNESS_LVL_5                99

#define HAL_DISP_BRIGHTNESS_SLEEP           4
#define HAL_DISP_BRIGHTNESS_INDOOR          25
#define HAL_DISP_BRIGHTNESS_OUTDOOR         70

#if defined(MODEL_P1_STM32F469_479) || defined(MODEL_K1_STM32F469_479) || defined(MODEL_AK811_STM32F469_479) || defined(MODEL_P3_AM6231)

#define HAL_SCREEN_TIMEOUT_ACTION           HAL_SCREEN_TIMEOUT_ACTION_DIM

#elif defined(MODEL_P2_AT32F437)

#define HAL_SCREEN_TIMEOUT_ACTION           HAL_SCREEN_TIMEOUT_ACTION_SLEEP

#endif

#if defined(MODEL_AK811_STM32F469_479)

#define HAL_DISP_HOR_RES                     240
#define HAL_DISP_VER_RES                     320

#else

#define HAL_DISP_HOR_RES                     280
#define HAL_DISP_VER_RES                     240

#endif

/******************************************************************************
  
                                按键配置
  
 ******************************************************************************/

/**
 * UI_KEY_FUNC_VOL_CTRL                 音量控制
 * UI_KEY_FUNC_SWITCH_GRP               切换守候组
 * UI_KEY_FUNC_PROGRAMMABLE             自定义功能, 由写频软件配置
 * UI_KEY_FUNC_2PTT                     2PTT
 * UI_KEY_FUNC_CANCEL                   拆线
 */

/******************************************************************************
  
                                待机页面配置
  
 ******************************************************************************/

/******************************************************************************
  
                                菜单页面配置
  
 ******************************************************************************/

#if defined(MODEL_AK811_STM32F469_479)

#define UI_MENU_ITEM_COUNT 5

#else

#define UI_MENU_ITEM_COUNT 4

#endif

/******************************************************************************
  
                                信息页面配置
  
 ******************************************************************************/

#if defined(MODEL_AK811_STM32F469_479)

#define UI_INFO_ITEM_COUNT 4

#else

#define UI_INFO_ITEM_COUNT 3

#endif

/******************************************************************************
  
                                硬件接口配置
  
 ******************************************************************************/

/******************************************************************************
  
                                PC模拟器配置
  
 ******************************************************************************/

#define UI_USE_SIMULATOR                        0

#if UI_USE_SIMULATOR
    #if USE_SDL
        #define UI_SIMULATOR_INCLUDE            <SDL2/SDL.h>
        #define LV_DRV_SDL_INCLUDE              "lv_drivers/sdl/sdl.h"
    #else
        #error "No simulator is selected"
    #endif
#endif

#endif

#endif
